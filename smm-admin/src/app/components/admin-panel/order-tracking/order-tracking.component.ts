import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { AdminOrder, AdminOrderSearchRequest } from '../../../model/response/admin-order.model';
import { AdminPanelTenant } from '../../../model/response/admin-panel-tenant.model';
import { AdminPanelService } from '../../../core/services/admin-panel.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-order-tracking',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule
  ],
  templateUrl: './order-tracking.component.html',
  styleUrl: './order-tracking.component.css'
})
export class OrderTrackingComponent implements OnInit, OnD<PERSON>roy {
  // Make Math available in template
  Math = Math;

  // Orders data
  orders: AdminOrder[] = [];
  isLoading = false;
  
  // Pagination
  pagination = {
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  };

  // Search and filters
  searchTerm = '';
  selectedTenantId = '';
  selectedStatus = '';
  startDate = '';
  endDate = '';
  hasAppliedFilters = false;

  // Available tenants for filter
  tenants: AdminPanelTenant[] = [];

  // Order statuses
  orderStatuses = [
    { value: '', label: 'ALL STATUS' },
    { value: 'PENDING', label: 'PENDING' },
    { value: 'IN_PROGRESS', label: 'IN_PROGRESS' },
        { value: 'PROCESSING', label: 'PROCESSING' },
    { value: 'COMPLETED', label: 'COMPLETED' },
    { value: 'PARTIAL', label: 'PARTIAL' },
    { value: 'CANCELED', label: 'CANCELED' },
    { value: 'FAILED', label: 'FAILED' }
  ];

  private subscriptions: Subscription[] = [];

  constructor(private adminPanelService: AdminPanelService) {}

  ngOnInit() {
    this.loadTenants();
    // Load orders with default filters on init
    this.hasAppliedFilters = true; // Mark as applied since we're doing initial load
    this.loadOrders();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadTenants() {
    // Load tenants for filter dropdown
    const sub = this.adminPanelService.getAllPanels(0, 1000).subscribe({
      next: (response) => {
        this.tenants = response.content;
      },
      error: (error) => {
        console.error('Error loading tenants:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  loadOrders(page: number = 0) {
    // Only load orders if filters have been applied
    if (!this.hasAppliedFilters) {
      return;
    }

    this.isLoading = true;

    const searchRequest: AdminOrderSearchRequest = {
      search: this.searchTerm || undefined,
      tenantId: this.selectedTenantId || undefined,
      status: this.selectedStatus || undefined,
      startDate: this.startDate || undefined,
      endDate: this.endDate || undefined,
      pageNumber: page,
      pageSize: this.pagination.page_size
    };

    const sub = this.adminPanelService.getAllOrders(searchRequest).subscribe({
      next: (response) => {
        this.orders = response.content;
        this.pagination = {
          page_number: response.pageable.page_number,
          page_size: response.pageable.page_size,
          total_elements: response.total_elements,
          total_pages: response.total_pages
        };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading orders:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  onSearch() {
    this.hasAppliedFilters = true;
    this.loadOrders(0);
  }

  getDomainName(tenantId: string): string {
    const tenant = this.tenants.find(t => t.id === tenantId);
    return tenant ? tenant.domain : tenantId;
  }

  // Removed onFilterChange() - filters only apply when user clicks Apply Filter button

  clearFilters() {
    this.searchTerm = '';
    this.selectedTenantId = '';
    this.selectedStatus = '';
    this.startDate = '';
    this.endDate = '';
    this.hasAppliedFilters = false;
    this.orders = []; // Clear orders when filters are cleared
    this.pagination = {
      page_number: 0,
      page_size: 10,
      total_elements: 0,
      total_pages: 0
    };
  }

  onPageChange(page: number) {
    if (this.hasAppliedFilters) {
      this.loadOrders(page);
    }
  }

  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'PARTIAL':
        return 'bg-orange-100 text-orange-800';
      case 'CANCELED':
      case 'CANCELED_WITHOUT_REFUND':
        return 'bg-red-100 text-red-800';
      case 'FAILED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getTenantName(tenantId: string): string {
    const tenant = this.tenants.find(t => t.id === tenantId);
    return tenant ? tenant.domain : tenantId;
  }
}
